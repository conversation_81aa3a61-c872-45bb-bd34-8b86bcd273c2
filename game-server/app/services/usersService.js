var logger          = require('pomelo-logger').getLogger('event-log', __filename);
var consts          = require('../consts/consts');
const LogTypes      = require('../consts/logTypes');
const LogBoards     = require('../consts/logBoards');
var userDao         = require('../dao/userDao');
var utils           = require('../util/utils');
var _               = require("underscore");
var CODE            = require('../consts/code');
var Sequelize       = require('sequelize');
//var elasticClient   = require('../dao/elastic/elastic');
var resultCode      = require('../consts/result');
var async           = require('async');
var messageService  = require('./messageService');
//var handUtils       = require('../util/handUtils');
var pokerUtils      = require('../util/pokersolver').Hand;
const logsDao           = require('../dao/logsDao');
const { LogsCusDTO }    = require('../domain/entity/logsDto')
var EVENT_SETTINGS      = require('../../config/eventSettings.json');
const { PlayerLiteDTO } = require('../domain/entity/player');
const { BadgeDTO }      = require('../domain/entity/badgeDTO');

/**
 * Get Elastic config
 */
// ------------------------------------------------------------------------------------------------------------
var elasticConfig   = require('../../config/configOther.json');
var env = process.env.NODE_ENV || 'development';
if (elasticConfig[env]) {
    elasticConfig = elasticConfig[env].elastic;
}

var UsersService = function(app){
    this.app = app;
    this.prefix = "USER:";
};

module.exports = UsersService;


/**
 * Hàm xử lý lấy thông tin người chơi khi view info
 * @param uid là cột id của player and auto increment
 * @param cb
 */
UsersService.prototype.getUserInfoHandler = function (uid, cb) {
    const me = this;
    var winNr, totalNr, winRate, playJson, biggestWon, bestHand, codeWin, hand, cardList, favoriteTable;
    // userDao.getPlayerByUid(uid, function (e, user) {
    me.app.rpc.db.dbRemote.getPlayerById('*', uid, async function(e, code, user) {
    // me.app.rpc.db.dbRemote.getPlayerByUserId('*', uid, async function(e, code, user) {
        logger.info("[usersService.getUserInfoHandler] user: ", user, ' -> code: ', code, ' -> e: ', e);
        if (code !== 200) {
            cb(code, null);
            return;
        }

        winNr           = Number(user?.win ?? 0);
        totalNr         = Number(user?.total ?? 0);
        winRate         = (winNr > 0 && totalNr > 0) ? (winNr/totalNr)*100 : 0;
        biggestWon      = "0";
        bestHand        = "";
        codeWin         = 600;
        hand            = "";
        cardList        = "";
        logger.info("[usersService.getUserInfoHandler] winNr: ", winNr, ' -> totalNr: ', totalNr, ' -> winRate: ', winRate);
        // favoriteTable   =  {
        //     "smallBlind": 200,
        //     "bigBlind": 400,
        //     "minPlayers": 2,
        //     "maxPlayers": 9,
        //     "minBuyIn": 4000,
        //     "maxBuyIn": 80000,
        //     "gameMode": "normal"
        // };

        //logger.info("abadfadfasdf: ", typeof user.properties.playJson);

        // if (user.properties.playJson){
        if (user?.properties?.play_json){
            // playJson    = JSON.parse(user.properties.playJson);
            playJson    = JSON.parse(user?.properties?.play_json);
            biggestWon  = playJson?.biggestWon ?? '';
            bestHand    = playJson?.bestHand ?? '';
            codeWin     = playJson?.codeWin ?? '';

            if (typeof bestHand === "object") {
                hand = pokerUtils.solve(bestHand);
                var _cardList = pokerUtils.showListCard(hand);
                // cardList = hand.myCard;
                cardList = {
                    listCard: hand.myCard,
                    name: _cardList.name
                }
            }
        }

        // lấy thông tin user từ grpc authen
        // ------------------------------------------------------------------------------------------------
        const userIdOfUser = user?.user_id ?? 0;
        const { codeUserInfo, resUserInfo } = await new Promise((resolve, reject) => {
            me.app.rpc.authen.authenRemote.getUserInfo('*', userIdOfUser, (err, codeUserInfo, resUserInfo) => {
                if (err) reject(err);
                else resolve({ codeUserInfo, resUserInfo });
            });
        });
        logger.info("[usersService.getUserInfoHandler] resUserInfo: ", resUserInfo, ' -> codeUserInfo: ', codeUserInfo);

        let name = user?.username ?? '';
        // if (!user.fullname) {
        //     name = user.username;
        // }
        let uid = 0;
        if (resUserInfo?.success) {
            // user = resUserInfo.data;
            name = resUserInfo?.data?.full_name ?? '';
            uid = resUserInfo?.data?.id ?? 0;
        }

        // // lấy thông tin tiền từ grpc payment
        // // ------------------------------------------------------------------------------------------------
        // const idOfUser = user?.id ?? 0;
        // const { codeBalance, balanceRes } = await new Promise((resolve, reject) => {
        //     me.app.rpc.coin.coinRemote.getAccountInfo('*', { userId: idOfUser }, (err, codeBalance, balanceRes) => {
        //         if (err) reject(err);
        //         else resolve({ codeBalance, balanceRes });
        //     });
        // });
        // logger.info('getUserInfoHandler >> codeBalance', codeBalance, ' -> balanceRes: ', balanceRes);
        // let balance = 0;
        // if (Number(codeBalance) === 200) {
        //     balance = balanceRes?.balance ?? 0;
        // }

        let balance = 0;
        balance = resUserInfo?.data?.balance ?? 0;

        var userData = {
            id: user?.id ?? 0,
            // userId: user?.user_id ?? '',
            uid: uid, // user?.uid ?? 0,
            type: user?.type ?? 1,
            avatar: user?.avatar ?? 1,
            username: user?.username, //user.username,
            fullName: name, //user.fullname,
            balance: balance, // user?.balance ?? 0,
            total: totalNr,
            win: winNr,
            winRate:  Math.round(winRate),
            biggestWon: biggestWon,
            bestHand: cardList,
            // codeWin: codeWin,
            // favoriteTable: favoriteTable
        };
        cb(null, userData);

    });

};


/**
 * Xử lý lấy thông tin của 1 người chơi, bao gồm cả người đó đang chơi bàn nào, hay đang ở đâu
 * @param {number} uid 
 * @param {Function} cb 
 */
UsersService.prototype.getPlayerInfo = async function (uid, cb) {
    const me = this;
    // var winNr, totalNr, winRate, playJson, biggestWon, bestHand, codeWin, hand, cardList, favoriteTable;
    // userDao.getPlayerByUid(uid, function (e, user) {
    me.app.rpc.db.dbRemote.getPlayerById('*', uid, async function(e, code, user) {
    // me.app.rpc.db.dbRemote.getPlayerByUserId('*', uid, async function(e, code, user) {
        logger.info("[usersService.getPlayerInfo] >> user: ", user, ' -> code: ', code, ' -> e: ', e);
        if (code !== 200) {
            cb(code, null);
            return;
        }

        // get thông tin của người chơi trên trong game (memory)
        // ----------------------------------------------------------------------------------------------------
        const playerInGame = await new Promise((resolve, reject) => {
            me.app.rpc.manager.userRemote.getUserCacheByUid(null, uid, (res) => {
                resolve(res);
            });
        });

        logger.info("[usersService.getPlayerInfo] >> playerInGame: ", playerInGame);
        let roomId = null;
        let gameId = null;
        if (playerInGame) {
            roomId = playerInGame?.roomId ?? null;
            gameId = playerInGame?.gameId ?? null;
        }
        logger.info("[usersService.getPlayerInfo] >> roomId: ", roomId, ' -> gameId: ', gameId);

        // Get table info by gameId (nếu có)
        // ----------------------------------------------------------------------------------------------------
        let tableInfo = null;
        if (gameId) {
            const tableInfoByGameId = await new Promise((resolve, reject) => {
                me.app.rpc.game.tableRemote.getTable(null, gameId, (res) => {
                    resolve(res);
                });
            });
            logger.info("[usersService.getPlayerInfo] >> tableInfoByGameId: ", tableInfoByGameId);
            if (tableInfoByGameId && tableInfoByGameId.status) {
                tableInfo = tableInfoByGameId?.data || null
            }
        }
        logger.info("[usersService.getPlayerInfo] >> tableInfo: ", tableInfo);

        // lấy thông tin user từ grpc authen (lấy ra giới tính)
        // ------------------------------------------------------------------------------------------------
        const { codeUserInfo, resUserInfo } = await new Promise((resolve, reject) => {
            me.app.rpc.authen.authenRemote.getUserById('*', uid, (err, codeUserInfo, resUserInfo) => {
                if (err) reject(err);
                else resolve({ codeUserInfo, resUserInfo });
            });
        });
        logger.info("[usersService.getPlayerInfo] >> resUserInfo: ", resUserInfo, ' -> codeUserInfo: ', codeUserInfo);
        let gender = '';
        if (codeUserInfo === 200)  {
            gender = resUserInfo?.gender || '';
        }

        let userDataCb = {
            player_info: user,
            table_info: tableInfo
        };
        // append thêm giới tính vào player_info
        userDataCb.player_info.gender = gender;

        cb(null, userDataCb);
    });

}; // end getPlayerInfo


UsersService.prototype.getUsersLobby = function (uid, cb) {

    var me, userTmp, newUsers;
    // me.app.rpc.manager.userRemote.getUsersLobby(null, uid, function (res) {
    //     logger.info("getUsersLobby >> res: ", res);
    //     cb(res);
    // });

    me = this;
    userTmp = {};
    newUsers = [];

    async.waterfall([
        function(callback) {
            // Step 1: lấy danh sách users có trong cached
            // ---------------------------------------------------------------------------------------------------------
            me.app.rpc.manager.userRemote.getUsersLobby(null, uid, function (res) {
                callback(null, res);
            });
        },
        function(users, callback) {
            // Step 2: danh sách users lấy được ở bước 1
            // ---------------------------------------------------------------------------------------------------------
            logger.info("users from step 1: ", users);
            if (users.length <= 0) {
                callback(null, []);
            }else{
                _.each(users, function (item) {
                    const player = item.player;
                    logger.info("userTmp : ", userTmp);
                    logger.info("item : ", item);
                    logger.info("player : ", player);
                    //userTmp = _.pick(userTmp, 'uid', 'username', 'fullname', 'avatar', 'balance');
                    userTmp = {
                        id: player.id,
                        // user_id: player.user_id,
                        user_id: player.uid,
                        uid: player.uid,
                        // displayName: (player.fullname != null) ? player.fullname : player.username,
                        // displayName: (player.full_name != null) ? player.full_name : player.username,
                        displayName: player.full_name || player.display_name || player.username,
                        type: player.type,
                        balance: player.balance,
                        avatar: player.avatar,
                        // f_uid: player.uid
                    };
                    newUsers.push(userTmp);
                    // reset userTmp
                    userTmp = {};
                });

                logger.info("newUsers: ", newUsers);

                callback(null, newUsers);
            }
        }
    ], function (err, result) {
        // result now equals 'done'
        // -------------------------------------------------------------------------------------------------------------
        logger.info("data cuoi cung err ", err, " result: ", result);
        return cb(result);
    });

};

/**
 * Xử lý hành động mời chơi của người chơi với nhau
 * @param data
 * @param cb
 */
UsersService.prototype.invitePlayHandler = function (data, cb) {
    logger.info("[usersService.invitePlayHandler] with data: ", data);
    var uid, username, fid, tid, pos, me;
    // uid         = data.uid, username = data.username, fid = data.fid, tid = data.tid, pos = data.pos;
    uid         = data.uid, fid = data.fid, tid = data.tid, pos = data.pos;
    me          = this;
    // let sidOfUser = null; // serverId: 'connector-server-1',
    // let userId = null, fullName = null, avatar = null;
    // var sessionService = me.app.get('sessionService');
    let playerLite = {
        id: '',
        uid: 0,
        user_id: '',
        full_name: '',
        username: '',
        avatar: "1"
    }

    async.waterfall([
        function(callback) {
            // Step 1: check tid có tồn tại hay hợp lệ không và lấy thông tin của bàn ra
            // ------------------------------------------------------------------------------------------------
            me.app.rpc.game.tableRemote.getTable(null, tid, function (data) {
                logger.info("[usersService.invitePlayHandler][Step 1] check data table response: ", data);
                // data return true | false
                callback(null, data);
            });
        },
        // Get thông tin của người gửi theo uid
        function(tableData, callback) {
            logger.info("[usersService.invitePlayHandler][Step 2] check data tableInfo: ", tableData, ' -> tiếp tục lấy thông tin người gửi request');
            // Step 2: check fid đang có mặt trong game và đang chơi game hay không
            // ------------------------------------------------------------------------------------------------
            if (tableData.status) {
                me.app.rpc.manager.userRemote.getUserCacheByUid(null, uid, function (user) {
                    logger.info("[usersService.invitePlayHandler][Step 2] check xem uid ", uid, " co trong game hay khong: ", user);
                    if (user) {
                        // sidOfUser = user?.serverId ?? null;
                        playerLite = {
                            id: user?.player?.id ?? '',
                            user_id: user?.player?.user_id ?? '',
                            uid: user?.player?.uid ?? 0,
                            full_name: user?.player?.full_name ?? '',
                            username: user?.player?.username ?? '',
                            avatar: user?.player?.avatar ?? ''
                        } 
                        callback(null, tableData);
                    }else{
                        callback(null, tableData);
                    }
                });
            } else {
                callback(null, tableData);
            }
        },
        function(data, callback) {
            logger.info("[usersService.invitePlayHandler][Step 3] callback check data tableInfo: ", data);
            // Step 3: check fid đang có mặt trong game và đang chơi game hay không
            // ------------------------------------------------------------------------------------------------
            if (data.status) {
                me.app.rpc.manager.userRemote.getUserCacheByUid(null, fid, function (user) {
                    logger.info("[usersService.invitePlayHandler][Step 3] check xem fid ", fid, " co trong game hay khong: ", user);
                    if (user) {
                        // sidOfUser = user?.serverId ?? null;
                        callback(null, true, data);
                    }else{
                        callback(null, false, data);
                    }
                });
            } else {
                callback(null, false, data);
            }
        },
        async function(status, args, callback) {
            logger.info("[usersService.invitePlayHandler][Step 4] status: ", status, ' -> tableInfo: ', args);
            // Step 3: send message về cho người được mời để hiển thị thông báo mời chơi
            // ------------------------------------------------------------------------------------------------
            if (status) {
                // args = true => push message cho nguoi duoc moi
                var _cmdPush = consts.GAME.ROUTER.INVITE_PLAY;
                var _arrMsg = {
                    // username: username,
                    sender: playerLite,
                    // tid: tid,
                    table: args.data
                };
                var _code = CODE.OK;
                await messageService.pushMessageByUid(fid, _cmdPush, _arrMsg, _code);
            }
            logger.info("[usersService.invitePlayHandler][Step 3] callback data");
            callback(null, 'done');
        }
    ], function (err, result) {
        logger.info("[usersService.invitePlayHandler][Step 4] result:", result, ' -> err: ', err);
        // Step 4: return kết thúc
        // ----------------------------------------------------------------------------------------------------
        return cb();
    });
};


/**
 * Hàm xử lý hành động tip thưởng cho dealer
 * @param uid id của người tip
 * @param amount số tiền/số lượng
 * @param type loại tip: CHIP, PROPS
 * @param cb
 */
UsersService.prototype.tipDealerHandler = function (uid, amount, type, cb) {
    var balance, me, codeRes;
    me = this;

    const dbService = this.app.get('dbService');

    async.waterfall([
        function(callback) {
            // Step 1: lấy thông tin player theo uid
            // userDao.getPlayerByUid(uid, callback);
            dbService.getPlayerById(uid, (err, code, playerInfo) => {
                logger.info("[Step 1] >> tipDealerHandler >> playerInfo: ", playerInfo);
                if (err || code !== CODE.OK) {
                    logger.error('[Step 1] Player not found:', err);
                    return callback('Player not found');
                } else {
                    logger.info('[Step 1] Found player:', playerInfo?.id);
                    callback(null, playerInfo);
                }
            });
        },
        function(playerInfo, callback) {
            // Step 2: kiểm tra xem số tiền còn lại của user có đủ để tip hay không
            // - Nếu có thì thực hiện trừ tiền của player trong database
            logger.info("[Step 2] >> tipDealerHandler >> playerInfo: ", playerInfo);
            if (playerInfo) {
                balance = playerInfo?.balance || 0;
                if (balance > amount) {
                    me.app.rpc.db.dbRemote.incrementPlayerStats(null, uid, { balance: -amount }, function (err, code, response) {
                        logger.info('[userService.tipDealerHandler] incrementPlayerStats: ', err, ' -> code: ', code, ' -> response: ', response);
                        if (err) {
                            logger.error('[userService.tipDealerHandler] failed!: ', err, JSON.stringify(val));
                            return cb(CODE.FAIL);    
                        }
                        callback(null, playerInfo);    
                    });
                } else {
                    // return về mã số tiền không đủ để tip
                    return cb(CODE.USER.NOT_ENOUGH_MONEY);
                }
            }else{
                // return false luôn mã không tồn tại player
                return cb(CODE.NOT_FOUND);
            }
        },
        function(playerInfo, callback) {
            // Step 3: Ghi logs transaction và cập nhật tiền vào cached server tương ứng với player đó
            logger.info("[Step 3][userService.tipDealerHandler] playerInfo: ", playerInfo);
            const transactionData = {
                player_id: uid,
                amount: -amount,
                type: consts.MODULE.TIPDEALER,
                action: consts.TIPDEALER.CHIP,
                before_balance: balance,
                after_balance: balance - amount,
                meta: {},
                description: `Tip dealer ${amount} chips`
            };
            logger.info('[Step 3][userService.tipDealerHandler] transactionData:', transactionData);
            
            dbService.createTransaction(transactionData, (err, code, result) => {
                if (err || code !== 200) {
                    logger.error(`[userService.tipDealerHandler][Step 3] Error recording transaction for player ${uid}:`, err || result);
                    return cb(CODE.FAIL);
                } else {
                    logger.info(`[userService.tipDealerHandler][Step 3] Transaction recorded successfully for player ${uid} result:`, result);
                    callback(null, playerInfo);
                }
            });
        },
        function(arg1, callback) {
            // Step 4: Get lại thông tin player sau khi bị trừ tiền từ database
            logger.info("[Step 4][userService.tipDealerHandler] arg1: ", arg1);
            // userDao.getPlayerByUid(uid, callback);
            dbService.getPlayerById(uid, (err, code, playerInfo) => {
                logger.info("[Step 4] >> tipDealerHandler >> playerInfo: ", playerInfo);
                if (err || code !== CODE.OK) {
                    logger.error('[Step 4] Player not found:', err);
                    return callback('Player not found');
                } else {
                    logger.info('[Step 4] Found player:', playerInfo?.id);
                    callback(null, playerInfo);
                }
            });
        },
        function (userInfo, callback) {
            // Step 5: cập nhật thông tin tiền mới vào userCached trên memory server
            logger.info("[Step 5][userService.tipDealerHandler] userInfo: ", userInfo);
            me.app.rpc.manager.userRemote.incrUserCachedBalance(null, uid, amount, function (error, user) {
                logger.info("[Step 5][userService.tipDealerHandler] Thông tin user from useCached: ", user, ' -> error: ', error);
                callback(userInfo);
            });
        }
    ], function (result) {
        // End: return về trạng thái cập nhật thành công + số tiền hiện tại của users
        logger.info("[Step 6][End] >> tipDealerHandler >> END result ", result);
        codeRes = CODE.FAIL;
        if (result) {
            codeRes = CODE.OK;
            // send notification to UPDATE_MYSELF
            messageService.pushMessageByUid(
                uid,
                consts.GAME.ROUTER.UPDATE_MYSELF,
                {
                    player: result,
                    balance: result?.balance || 0
                },
                CODE.USER.TIP_DEALER
            ).then(() => {
                logger.info(`[Step 6][userService.tipDealerHandler] Đã gửi thông báo onUpdateMyself cho người chơi ${uid}`);
            }).catch((pushError) => {
                logger.error(`[Step 6][userService.tipDealerHandler] Lỗi khi gửi thông báo cho người chơi ${uid}:`, pushError.message || pushError.stack);
            });
        }

        return cb(codeRes, result);
    });

};

UsersService.prototype.getMessagesAndEmailsHandler = async function (uid, cb) {
    logger.info("[usersService.getMessagesAndEmailsHandler] >> uid: ", uid);

    const dayAgo = EVENT_SETTINGS.emailDayAgoConfig.dayNum // 2; // 7 (default)
    try {
        const [emailLogs, systemLogs] = await Promise.all([
            new Promise((resolve, reject) => {
                logsDao.getLogs({ userId: uid, type: LogTypes.EMAIL, dayAgo: dayAgo }, (err, code, res) => {
                    // logger.info("getMessagesAndEmailsHandler >> getLogs >> EMAIL:: ", err, ' -> code: ', code, ' -> res: ', res);
                    if (err) reject(err);
                    else resolve(res);
                });
            }),
            new Promise((resolve, reject) => {
                logsDao.getLogs({ userId: uid, type: LogTypes.SYSTEM, dayAgo: dayAgo }, (err, code, res) => {
                    // logger.info("getMessagesAndEmailsHandler >> getLogs >> SYSTEM:: ", err, ' -> code: ', code, ' -> res: ', res);
                    if (err) reject(err);
                    else resolve(res);
                });
            })
        ]);

        const combinedLogs = {
            emails: emailLogs,
            systems: systemLogs
        };
        // logger.info("getMessagesAndEmailsHandler >> systemLogs: ", systemLogs);

        combinedLogs.systems = systemLogs.map(systems => new LogsCusDTO(systems));
        combinedLogs.emails = emailLogs.map(email => new LogsCusDTO(email));

        logger.info("[usersService.getMessagesAndEmailsHandler] >> combinedLogs2: ", combinedLogs);

        cb(null, combinedLogs);
        return;
    } catch (err) {
        cb(err, null);
        return;
    }
};

/**
 * Tìm kiếm người chơi theo tên hoặc uid trong bảng players
 * @param {Object} data - Dữ liệu tìm kiếm (uid, keyword, page, limit)
 * @param {Function} cb - Hàm callback
 */
UsersService.prototype.searchPlayer = function (data, cb) {
    logger.info("[usersService.searchPlayer][Step 0] searchPlayer >> data: ", data);
    const userId = data.uid; // ID của người tìm kiếm
    const keyword = data.keyword || ""; // Từ khóa tìm kiếm
    const page = data.page || 1; // Trang hiện tại
    const limit = data.limit || 10; // Số lượng kết quả trên một trang
    const me = this;

    async.waterfall([
        function(callback) {
            // Step 1: Tìm kiếm người chơi trong bảng players theo keyword
            const searchQuery = {
                keyword: keyword,
                page: page,
                limit: limit,
                excluded_id: userId // Loại trừ chính người tìm kiếm
            };

            me.app.rpc.db.dbRemote.searchPlayer('*', searchQuery, (err, code, result) => {
                if (err) {
                    logger.error('[usersService.searchPlayer][Step 1] searchPlayer Error:', err);
                    return cb(true, err);
                } else {
                    logger.info('[usersService.searchPlayer][Step 1] searchPlayer Result:', result);
                    callback(null, result);
                }
            });
        },
        function (data, callback) {
            logger.info("[usersService.searchPlayer][Step 2] searchPlayer data result: ", data);

            // Step 2: Kiểm tra trạng thái online của các người chơi tìm thấy
            if (data.totalItems > 0) {
                me.app.rpc.manager.userRemote.getUsersCache(null, function (userCacheList) {
                    logger.info("[usersService.searchPlayer][Step 2.1] searchPlayer User cache: ", userCacheList?.length || 0);

                    // Tạo map từ danh sách người dùng online
                    var usersMap = {};
                    _.each(userCacheList, function(item) {
                        usersMap[item.uid] = item;
                    });

                    // Cập nhật trạng thái online cho kết quả tìm kiếm
                    _.each(data.players, function(player) {
                        if (usersMap[player.id]) {
                            player.is_online = true;
                        } else {
                            player.is_online = false;
                        }
                    });

                    callback(null, data);
                });
            } else {
                logger.info("[usersService.searchPlayer][Step 2.2] Không có kết quả tìm kiếm");
                callback(null, data);
            }
        },
        function (data, callback) {
            logger.info("[usersService.searchPlayer][Step 3] searchPlayer data User Online/Offline: ", data);

            if (!data.friends || data.friends.length === 0) {
                return callback(null, {
                    friends: [],
                    // users: [],
                    totalItems: 0,
                    totalPages: 0,
                    currentPage: page
                });
            }

            // Step 3: Lấy thông tin chi tiết từ service coin
            const listUserIds = _.pluck(data.friends, 'id');
            logger.info("[usersService.searchPlayer][Step 3.1] searchPlayer listUserIds: ", listUserIds);

            // Gọi coin service để lấy thêm thông tin người dùng
            // me.app.rpc.coin.coinRemote.getAccountInfoByUserIds('*', { userIds: listUserIds }, (err, code, response) => {
            me.app.rpc.authen.authenRemote.getUserInfoByIds('*', { ids: listUserIds }, (err, code, response) => {
                logger.info("[usersService.searchPlayer][Step 3.2] searchPlayer >> getUserInfoByIds ", response?.success, " >> err: ", err, " >> code: ", code);
                if (!response?.success) {
                    logger.error('[usersService.searchPlayer][Step 3.3] searchPlayer Error:', err);
                    return cb(true, err);
                } else {
                    logger.info('[usersService.searchPlayer][Step 3.4] searchPlayer Result items count:', response?.items?.length);
                    callback(null, {
                        friends: data.friends, // thông tin players trong database
                        users: response.items, // thông tin của coin data
                        totalItems: data.totalItems,
                        totalPages: data.totalPages || Math.ceil(data.totalItems / limit),
                        currentPage: data.currentPage || page
                    });
                }
            });
        },
        // get list my friends by uid
        function (data, callback) {
            logger.info("[usersService.searchPlayer][Step 4] searchPlayer data: ", data);
            if (!data.friends || data.friends.length === 0) {
                return callback(null, data);
            }

            // Step 4: Lấy danh sách bạn bè của người tìm kiếm
            me.app.rpc.db.dbRemote.getFriendsByTypes('*', { uid: userId, types: [1, 2, 3], limit: 100 }, (err, code, response) => {  
                logger.info("[usersService.searchPlayer][Step 4.1] searchPlayer >> getFriendsByTypes ", response, " >> err: ", err, " >> code: ", code);
                if (err) {
                    logger.error('[usersService.searchPlayer][Step 4.2] searchPlayer Error:', err);
                    return cb(true, err);
                } else {
                    logger.info('[usersService.searchPlayer][Step 4.3] searchPlayer Result items count:', response?.items?.length);
                    callback(null, {
                        friends: data.friends, // thông tin players trong database
                        users: data.users, // thông tin của coin data của các users
                        my_friends: response?.friends || [],
                        totalItems: data.totalItems,
                        totalPages: data.totalPages || Math.ceil(data.totalItems / limit),
                        currentPage: data.currentPage || page
                    });
                }
            });
        }
    ], function (err, result) {
        // logger.info("[Step 4] searchPlayer - Kết quả cuối cùng: ", result?.friends?.length);
        logger.info("[usersService.searchPlayer][Step 4] searchPlayer - Kết quả cuối cùng: ", result);

        if (err) {
            return cb(true, "Error processing search request");
        }

        let formattedResults = [];

        // Kết hợp thông tin từ friends và users
        _.each(result.friends, function (player) {
            const userInfo = result.users?.find(user => user.id === player.id);

            // find item in my_friends by f_player_id
            const myFriend = result.my_friends?.find(friend => friend.f_player_id === player.id);
            logger.info("[usersService.searchPlayer][Step 4.4] searchPlayer myFriend: ", myFriend, ' -> userInfo: ', userInfo);
            let type = -1;
            if (myFriend) {
                type = myFriend.type;
            }

            // Kết hợp thông tin từ cả hai nguồn
            const playerData = {
                id: player.id,
                uid: player.uid || player.id,
                nick_name: player.nick_name || player.username ||  userInfo.name || '',
                avatar: player.avatar || "1",
                level: player.level || 0,
                exp: player.exp || 0,
                win: player.win || 0,
                lost: player.lost || 0,
                total: player.total || 0,
                win_rate: player.win_rate || 0,
                is_online: player.is_online || false,

                // Thông tin từ user service
                balance: myFriend?.player?.balance || 0,
                full_name: userInfo?.full_name || player.nick_name,
                username: userInfo?.username || player.nick_name,
                type: type,
                gender: userInfo?.gender || ''
            };

            // Kiểm tra trạng thái kết bạn
            playerData.is_friend = false; // Mặc định là chưa kết bạn
            playerData.is_request_sent = false; // Có đang gửi lời mời kết bạn không
            playerData.friend_status = "none"; // Trạng thái kết bạn: none, requested, friends, blocked

            // Chuyển đổi sang DTO
            const playerDTO = new PlayerLiteDTO(playerData);
            formattedResults.push(playerDTO);
        });

        logger.info("[usersService.searchPlayer][Step 4.1] searchPlayer - Kết quả đã định dạng: ", formattedResults);

        return cb(null, {
            friends: formattedResults,
            totalItems: result?.totalItems || 0,
            totalPages: result?.totalPages || 1,
            currentPage: result?.currentPage || page
        });
    });
};

/**
 * Nhận thưởng từ thành tích
 * @param {Object} payload - Dữ liệu đầu vào (userId, badgeId)
 * @param {Function} cb - Callback function
 */
UsersService.prototype.claimBadgeRewardHandler = function(payload, cb) {
    logger.info("[UsersService.claimBadgeRewardHandler][Step 0] Nhận thưởng từ thành tích >> payload: ", payload);
    const userId = payload.userId;
    const badgeId = payload.badgeId;

    const me = this;

    if (!userId || !badgeId) {
        return cb(true, CODE.BAD_REQUEST, "Thiếu thông tin userId hoặc badgeId");
    }

    async.waterfall([
        // Bước 1: Kiểm tra player có tồn tại
        function(callback) {
            me.app.rpc.db.dbRemote.getPlayerById('*', userId, (err, code, player) => {
                if (err || code !== CODE.OK) {
                    logger.error('[Step 1] Người chơi không tồn tại:', err);
                    return cb(true, CODE.NOT_FOUND, "Người chơi không tồn tại");
                } else {
                    logger.info('[Step 1] Tìm thấy người chơi:', player?.id);
                    callback(null, player);
                }
            });
        },

        // Bước 2: Kiểm tra badge và quyền sở hữu
        function(player, callback) {
            logger.info('[Step 2] Kiểm tra badge và quyền sở hữu của player_id:', player.id);

            me.app.rpc.db.dbRemote.getBadgeDetails('*', {
                badge_id: badgeId,
                player_id: player.id
            }, (err, code, badgeDetails) => {
                if (err || code !== CODE.OK) {
                    logger.error('[Step 2] Lỗi khi lấy thông tin badge:', err);
                    return cb(true, CODE.NOT_FOUND, "Không tìm thấy thành tích này hoặc bạn không có quyền nhận thưởng");
                }

                if (!badgeDetails) {
                    return cb(true, CODE.NOT_FOUND, "Không tìm thấy thành tích này");
                }

                // Kiểm tra xem đã nhận thưởng chưa
                if (badgeDetails.claimed_at) {
                    logger.info('[Step 2] Badge đã được nhận thưởng trước đó:', badgeDetails.claimed_at);
                    return cb(true, CODE.BADGE.ALREADY_RECEIVED, "Thành tích này đã được nhận thưởng trước đó");
                }

                // kiểm tra xem đã đạt điều kiện nhận thưởng hay chưa
                if (badgeDetails.completion_percent < 100) {
                    logger.info('[Step 2] Chưa đủ điều kiện nhận thưởng:', badgeDetails.completion_percent);
                    return cb(true, CODE.BADGE.NOT_COMPLETED, "Chưa đủ điều kiện nhận thưởng");
                }

                callback(null, {
                    player: player,
                    badge: badgeDetails
                });
            });
        },

        // Bước 3: Lấy danh sách phần thưởng từ bảng badge_rewards
        function(data, callback) {
            logger.info('[Step 3] Lấy danh sách phần thưởng cho badge:', data.badge?.code);

            me.app.rpc.db.dbRemote.getBadgeRewards('*', {
                badge_id: badgeId
            }, (err, code, rewardsResult) => {
                if (err) {
                    logger.error('[Step 3] Lỗi khi lấy danh sách phần thưởng:', err);
                    return cb(true, CODE.FAIL, "Lỗi khi lấy thông tin phần thưởng");
                }

                const rewards = rewardsResult.rewards || [];
                logger.info(`[Step 3] Tìm thấy ${rewards.length} phần thưởng cho badge_id=${badgeId}`);

                // Nếu không có phần thưởng
                if (rewards.length === 0) {
                    logger.warn('[Step 3] Badge không có phần thưởng được cấu hình');
                    // Vẫn đánh dấu là đã claim nhưng không có phần thưởng
                    me.app.rpc.db.dbRemote.markBadgeAsClaimed('*', {
                        badge_id: badgeId,
                        player_id: data.player.id
                    }, (err, code, result) => {
                        if (err || code !== CODE.OK) {
                            logger.error('[Step 3] Lỗi khi đánh dấu đã nhận thưởng:', err);
                            return cb(true, CODE.FAIL, "Lỗi khi cập nhật trạng thái nhận thưởng");
                        }

                        // Trả về kết quả thành công nhưng không có phần thưởng
                        callback(null, {
                            badge: data.badge,
                            reward: {
                                type: "NONE",
                                value: 0
                            },
                            claimed_at: new Date(),
                            rewards: []
                        });
                    });

                    return;
                }

                // Đánh dấu badge là đã nhận thưởng trước khi xử lý phần thưởng
                me.app.rpc.db.dbRemote.markBadgeAsClaimed('*', {
                    badge_id: badgeId,
                    player_id: data.player.id
                }, async (err, code, claimResult) => {
                    if (err || code !== CODE.OK) {
                        logger.error('[Step 3] Lỗi khi đánh dấu đã nhận thưởng:', err);
                        return cb(true, CODE.FAIL, "Lỗi khi cập nhật trạng thái nhận thưởng");
                    }

                    // Xử lý từng phần thưởng
                    const processedRewards = [];
                    const rewardPromises = [];

                    for (const reward of rewards) {
                        const rewardType = reward.reward_type;
                        const rewardValue = reward.reward_value;
                        const isPermanent = reward.is_permanent;

                        // Tạo promise cho mỗi phần thưởng
                        const rewardPromise = new Promise((resolve, reject) => {
                            switch (rewardType) {
                                case 'CHIPS':
                                    // Cộng chips cho người chơi
                                    const chipsValue = parseInt(rewardValue, 10) || 0;

                                    // Lấy thông tin tài khoản hiện tại trước khi cập nhật
                                    // me.app.rpc.coin.coinRemote.getAccountInfo('*', { userId: data.player.id }, (getErr, getCode, accountInfo) => {
                                    me.app.rpc.db.dbRemote.getPlayerById('*', data.player.id, (getErr, getCode, accountInfo) => {
                                        if (getErr) {
                                            logger.error('[Step 3] Lỗi khi lấy thông tin tài khoản để cập nhật CHIPS:', getErr);
                                            reject({
                                                type: rewardType,
                                                value: rewardValue,
                                                error: "Lỗi khi lấy thông tin tài khoản để cập nhật CHIPS"
                                            });
                                            return;
                                        }

                                        const currentBalance = accountInfo?.balance || 0;
                                        const newBalance = currentBalance + chipsValue;

                                        logger.info(`[Step 3] Cập nhật CHIPS cho người chơi ${data.player.id} từ ${currentBalance} thành ${newBalance}`);

                                        // Chuẩn bị dữ liệu để cộng chips
                                        // const cashInData = {
                                        //     userId: data.player.id,
                                        //     amount: chipsValue,
                                        //     description: `Phần thưởng từ thành tích: ${data.badge?.badge?.name}`
                                        // };

                                        // Gọi coinRemote để cộng chips
                                        // me.app.rpc.coin.coinRemote.cashIn('*', cashInData, (err, code, response) => {
                                        me.app.rpc.db.dbRemote.incrementPlayerStats('*', data.player.id, { balance: chipsValue }, (err, code, response) => {
                                            if (err || code !== CODE.OK) {
                                                logger.error('[Step 3] Lỗi khi cộng chips:', err);
                                                reject({
                                                    type: rewardType,
                                                    value: rewardValue,
                                                    error: "Lỗi khi cập nhật chips"
                                                });
                                            } else {
                                                logger.info(`[Step 3] Đã cộng ${chipsValue} chips cho người chơi ${data.player.id}, số dư mới: ${response?.balance || newBalance}`);

                                                // Tạo log cho phần thưởng CHIPS vào table: logs
                                                try {
                                                    const logData = {
                                                        type: LogTypes.SYSTEM,
                                                        board: LogBoards.BADGE_REWARD_CHIPS, // 'BADGE_REWARD',
                                                        userId: data.player.id,
                                                        amount: chipsValue,
                                                        dataRaw: {
                                                            badge_id: data.badge?.badge?.id,
                                                            badge_name: data.badge?.badge?.name || '',
                                                            reward_type: rewardType,
                                                            reward_value: rewardValue,
                                                            timestamp: Math.floor(Date.now() / 1000),
                                                            old_balance: currentBalance,
                                                            new_balance: response?.balance || newBalance
                                                        }
                                                    };

                                                    me.app.get('sync').flush('playerSync.addLogs', data.player.id, logData);
                                                    // add logs transactions
                                                    me.app.get('sync').flush('playerSync.createTransaction', data.player.id, {
                                                        player_id: data.player.id,
                                                        amount: chipsValue,
                                                        before_balance: currentBalance,
                                                        after_balance: response?.balance || newBalance,
                                                        type: LogTypes.SYSTEM,
                                                        action: LogBoards.BADGE_REWARD_CHIPS,
                                                        reference_id: data.badge?.badge?.id,
                                                        reference_type: 'BADGE_REWARD_CHIPS',
                                                        meta: {
                                                            badge_id: data.badge?.badge?.id,
                                                            badge_name: data.badge?.badge?.name || '',
                                                            reward_type: rewardType,
                                                            reward_value: rewardValue,
                                                            old_balance: currentBalance,
                                                            new_balance: response?.balance || newBalance,
                                                            timestamp: Math.floor(Date.now() / 1000)
                                                        },
                                                        description: `Phần thưởng từ thành tích: ${data.badge?.badge?.name}`
                                                    });

                                                } catch (logError) {
                                                    logger.error(`[Step 3] Lỗi khi tạo log cho phần thưởng CHIPS:`, logError);
                                                }

                                                resolve({
                                                    type: rewardType,
                                                    value: chipsValue,
                                                    is_permanent: isPermanent,
                                                    success: true
                                                });
                                            }
                                        });
                                    });
                                    break;

                                case 'EXP':
                                    // Cộng EXP cho người chơi
                                    const expValue = parseInt(rewardValue, 10) || 0;

                                    // Lấy thông tin người chơi hiện tại để cập nhật EXP
                                    me.app.rpc.db.dbRemote.getPlayerById('*', data.player.id, (playerErr, playerCode, playerData) => {
                                        if (playerErr) {
                                            logger.error('[Step 3] Lỗi khi lấy thông tin người chơi để cập nhật EXP:', playerErr);
                                            reject({
                                                type: rewardType,
                                                value: rewardValue,
                                                error: "Lỗi khi lấy thông tin người chơi để cập nhật EXP"
                                            });
                                            return;
                                        }

                                        // Tính toán giá trị EXP mới
                                        const currentExp = playerData?.exp || 0;
                                        const newExp = currentExp + expValue;

                                        // Sử dụng giá trị số trực tiếp thay vì Sequelize.literal
                                        const updateData = {
                                            exp: newExp
                                        };

                                        logger.info(`[Step 3] Cập nhật EXP cho người chơi ${data.player.id} từ ${currentExp} thành ${newExp}`);

                                        me.app.rpc.db.dbRemote.updatePlayer('*', data.player.id, updateData, (err, code, response) => {
                                            if (err || code !== CODE.OK) {
                                                logger.error('[Step 3] Lỗi khi cộng EXP:', err);
                                                reject({
                                                    type: rewardType,
                                                    value: rewardValue,
                                                    error: "Lỗi khi cập nhật EXP"
                                                });
                                            } else {
                                                logger.info(`[Step 3] Đã cộng ${rewardValue} EXP cho người chơi ${data.player.id}, EXP mới: ${newExp}`);

                                                // Tạo log cho phần thưởng EXP
                                                try {
                                                    const logData = {
                                                        type: LogTypes.SYSTEM, // 'BADGE_REWARD',
                                                        board: LogBoards.BADGE_REWARD_EXP, // 'EXP',
                                                        userId: data.player.id,
                                                        amount: expValue,
                                                        dataRaw: {
                                                            badge_id: data.badge.badge_id,
                                                            badge_name: data.badge?.badge?.name || '',
                                                            reward_type: rewardType,
                                                            reward_value: rewardValue,
                                                            timestamp: Math.floor(Date.now() / 1000),
                                                            old_exp: currentExp,
                                                            new_exp: newExp
                                                        }
                                                    };

                                                    me.app.get('sync').flush('playerSync.addLogs', data.player.id, logData);

                                                } catch (logError) {
                                                    logger.error(`[Step 3] Lỗi khi tạo log cho phần thưởng EXP:`, logError);
                                                }

                                                resolve({
                                                    type: rewardType,
                                                    value: expValue,
                                                    is_permanent: isPermanent,
                                                    success: true
                                                });
                                            }
                                        });
                                    });
                                    break;

                                case 'VIP':
                                    // Cộng điểm VIP cho người chơi
                                    const vipValue = parseInt(rewardValue, 10) || 0;

                                    // Lấy thông tin người chơi hiện tại để cập nhật VIP point
                                    me.app.rpc.db.dbRemote.getPlayerById('*', data.player.id, (playerErr, playerCode, playerData) => {
                                        if (playerErr) {
                                            logger.error('[Step 3] Lỗi khi lấy thông tin người chơi để cập nhật VIP:', playerErr);
                                            reject({
                                                type: rewardType,
                                                value: rewardValue,
                                                error: "Lỗi khi lấy thông tin người chơi để cập nhật VIP"
                                            });
                                            return;
                                        }

                                        // Tính toán giá trị VIP point mới
                                        const currentVipPoint = playerData?.vip_point || 0;
                                        const newVipPoint = currentVipPoint + vipValue;

                                        // Sử dụng giá trị số trực tiếp thay vì Sequelize.literal
                                        const vipUpdateData = {
                                            vip_point: newVipPoint
                                        };

                                        logger.info(`[Step 3] Cập nhật điểm VIP cho người chơi ${data.player.id} từ ${currentVipPoint} thành ${newVipPoint}`);

                                        me.app.rpc.db.dbRemote.updatePlayer('*', data.player.id, vipUpdateData, (err, code) => {
                                            if (err || code !== CODE.OK) {
                                                logger.error('[Step 3] Lỗi khi cộng điểm VIP:', err);
                                                reject({
                                                    type: rewardType,
                                                    value: rewardValue,
                                                    error: "Lỗi khi cập nhật điểm VIP"
                                                });
                                            } else {
                                                logger.info(`[Step 3] Đã cộng ${rewardValue} điểm VIP cho người chơi ${data.player.id}, điểm VIP mới: ${newVipPoint}`);

                                                // Tạo log cho phần thưởng VIP
                                                try {
                                                    const logData = {
                                                        type: LogTypes.SYSTEM, // 'BADGE_REWARD',
                                                        board: LogBoards.BADGE_REWARD_VIP, // 'VIP',
                                                        userId: data.player.id,
                                                        amount: vipValue,
                                                        dataRaw: {
                                                            badge_id: data.badge.badge_id,
                                                            badge_name: data.badge?.badge?.name || '',
                                                            reward_type: rewardType,
                                                            reward_value: rewardValue,
                                                            timestamp: Math.floor(Date.now() / 1000),
                                                            old_vip_point: currentVipPoint,
                                                            new_vip_point: newVipPoint
                                                        }
                                                    };

                                                    me.app.get('sync').flush('playerSync.addLogs', data.player.id, logData);

                                                } catch (logError) {
                                                    logger.error(`[Step 3] Lỗi khi tạo log cho phần thưởng VIP:`, logError);
                                                }

                                                resolve({
                                                    type: rewardType,
                                                    value: vipValue,
                                                    is_permanent: isPermanent,
                                                    success: true
                                                });
                                            }
                                        });
                                    });
                                    break;

                                case 'AVATAR':
                                    // Thêm avatar mới cho người chơi
                                    // Giả sử có một hàm để thêm avatar cho người chơi
                                    logger.info(`[Step 3] Thêm avatar ${rewardValue} cho người chơi ${data.player.id}`);
                                    // TODO: Implement avatar reward logic
                                    resolve({
                                        type: rewardType,
                                        value: rewardValue,
                                        is_permanent: isPermanent,
                                        success: true
                                    });
                                    break;

                                case 'TITLE':
                                    // Thêm danh hiệu mới cho người chơi
                                    // Giả sử có một hàm để thêm danh hiệu cho người chơi
                                    logger.info(`[Step 3] Thêm danh hiệu ${rewardValue} cho người chơi ${data.player.id}`);
                                    // TODO: Implement title reward logic
                                    resolve({
                                        type: rewardType,
                                        value: rewardValue,
                                        is_permanent: isPermanent,
                                        success: true
                                    });
                                    break;

                                case 'DECORATION':
                                    // Thêm vật phẩm trang trí mới cho người chơi
                                    // Giả sử có một hàm để thêm vật phẩm trang trí cho người chơi
                                    logger.info(`[Step 3] Thêm vật phẩm trang trí ${rewardValue} cho người chơi ${data.player.id}`);
                                    // TODO: Implement decoration reward logic
                                    resolve({
                                        type: rewardType,
                                        value: rewardValue,
                                        is_permanent: isPermanent,
                                        success: true
                                    });
                                    break;

                                default:
                                    // Kiểu phần thưởng không được hỗ trợ
                                    logger.warn('[Step 3] Kiểu phần thưởng không được hỗ trợ:', rewardType);
                                    reject({
                                        type: rewardType,
                                        value: rewardValue,
                                        error: "Kiểu phần thưởng không được hỗ trợ"
                                    });
                            }
                        });

                        rewardPromises.push(rewardPromise);
                    }

                    // Xử lý tất cả các phần thưởng
                    try {
                        const results = await Promise.allSettled(rewardPromises);

                        // Lọc các phần thưởng đã xử lý thành công
                        const successfulRewards = results
                            .filter(result => result.status === 'fulfilled')
                            .map(result => result.value);

                        // Lọc các phần thưởng bị lỗi
                        const failedRewards = results
                            .filter(result => result.status === 'rejected')
                            .map(result => result.reason);

                        if (failedRewards.length > 0) {
                            logger.warn('[Step 3] Một số phần thưởng không thể xử lý:', failedRewards);
                        }

                        // Tìm phần thưởng chính để hiển thị (ưu tiên CHIPS, sau đó là EXP, sau đó là VIP)
                        let primaryReward = {
                            type: "NONE",
                            value: 0
                        };

                        const chipsReward = successfulRewards.find(r => r.type === 'CHIPS');
                        const expReward = successfulRewards.find(r => r.type === 'EXP');
                        const vipReward = successfulRewards.find(r => r.type === 'VIP');

                        if (chipsReward) {
                            primaryReward = {
                                type: "CHIPS",
                                value: chipsReward.value
                            };
                        } else if (expReward) {
                            primaryReward = {
                                type: "EXP",
                                value: expReward.value
                            };
                        } else if (vipReward) {
                            primaryReward = {
                                type: "VIP",
                                value: vipReward.value
                            };
                        } else if (successfulRewards.length > 0) {
                            primaryReward = {
                                type: successfulRewards[0].type,
                                value: successfulRewards[0].value
                            };
                        }

                        // Chuẩn bị kết quả để trả về
                        const result = {
                            badge: data.badge,
                            reward: primaryReward,
                            claimed_at: claimResult.claimed_at,
                            rewards: successfulRewards,
                            failed_rewards: failedRewards.length > 0 ? failedRewards : undefined
                        };
                        const badge_name = data.badge?.badge?.name || 'Unknown Badge';
                        // Bước 4: Gửi thông báo qua onUpdateMyself channel
                        try {
                            // Tạo thông báo cho từng loại phần thưởng
                            for (const reward of successfulRewards) {
                                let notificationMsg = null;

                                // Kiểm tra reward có hợp lệ không
                                if (!reward || !reward.type) {
                                    logger.warn(`[Step 4] Phần thưởng không hợp lệ:`, reward);
                                    continue;
                                }

                                // Lấy thông tin người chơi hiện tại để gửi kèm trong thông báo
                                let playerInfo = null;

                                // Tạo hàm để gửi thông báo sau khi có thông tin người chơi
                                const sendNotification = (playerInfo) => {
                                    let notificationMsg = null;

                                    switch (reward.type) {
                                        case 'CHIPS':
                                            notificationMsg = {
                                                type: 1009, // Mã thông báo cho thay đổi chips
                                                description: `Bạn nhận được phần thưởng ${reward.value.toLocaleString()} chips từ thành tích: ${badge_name}`,
                                                user_id: data.player.id,
                                                amount: reward.value,
                                                timestamp: Math.floor(Date.now() / 1000),
                                                badge_id: data.badge.badge_id,
                                                badge_name: badge_name,
                                                balance: playerInfo?.balance || 0, // Thêm số dư hiện tại
                                                reward_type: reward.type,
                                                reward_value: reward.value
                                            };
                                            break;

                                        case 'EXP':
                                            notificationMsg = {
                                                type: 1010, // Mã thông báo cho thay đổi EXP
                                                description: `Bạn nhận được phần thưởng ${reward.value.toLocaleString()} EXP từ thành tích: ${badge_name}`,
                                                user_id: data.player.id,
                                                amount: reward.value,
                                                timestamp: Math.floor(Date.now() / 1000),
                                                badge_id: data.badge.badge_id,
                                                badge_name: badge_name,
                                                exp: playerInfo?.exp || 0, // Thêm EXP hiện tại
                                                level: playerInfo?.level || 1, // Thêm level hiện tại
                                                reward_type: reward.type,
                                                reward_value: reward.value
                                            };
                                            break;

                                        case 'VIP':
                                            notificationMsg = {
                                                type: 1011, // Mã thông báo cho thay đổi VIP
                                                description: `Bạn nhận được phần thưởng ${reward.value.toLocaleString()} điểm VIP từ thành tích: ${badge_name}`,
                                                user_id: data.player.id,
                                                amount: reward.value,
                                                timestamp: Math.floor(Date.now() / 1000),
                                                badge_id: data.badge.badge_id,
                                                badge_name: badge_name,
                                                vippoint: playerInfo?.vippoint || 0, // Thêm điểm VIP hiện tại
                                                reward_type: reward.type,
                                                reward_value: reward.value
                                            };
                                            break;

                                        case 'AVATAR':
                                        case 'TITLE':
                                        case 'DECORATION':
                                            notificationMsg = {
                                                type: 1012, // Mã thông báo cho vật phẩm
                                                description: `Bạn nhận được phần thưởng ${reward.type.toLowerCase()}: ${reward.value} từ thành tích: ${badge_name}`,
                                                user_id: data.player.id,
                                                item_type: reward.type,
                                                item_value: reward.value,
                                                is_permanent: reward.is_permanent,
                                                timestamp: Math.floor(Date.now() / 1000),
                                                badge_id: data.badge.badge_id,
                                                badge_name: badge_name,
                                                reward_type: reward.type,
                                                reward_value: reward.value
                                            };
                                            break;
                                    }

                                    if (notificationMsg) {
                                        // Gửi thông báo qua messageService
                                        messageService.pushMessageByUid(
                                            data.player.id,
                                            consts.GAME.ROUTER.UPDATE_MYSELF,
                                            notificationMsg,
                                            notificationMsg.type
                                        ).then(() => {
                                            logger.info(`[Step 4] Đã gửi thông báo onUpdateMyself cho người chơi ${data.player.id} về phần thưởng ${reward.type}`);
                                        }).catch((pushError) => {
                                            logger.error(`[Step 4] Lỗi khi gửi thông báo cho phần thưởng ${reward.type}:`, pushError.message || pushError);
                                        });
                                    }
                                };

                                // Nếu là phần thưởng CHIPS, lấy thông tin tài khoản từ coinRemote
                                if (reward.type === 'CHIPS') {
                                    // me.app.rpc.coin.coinRemote.getAccountInfo('*', { userId: data.player.id }, (err, codeBalance, balanceRes) => {
                                    me.app.rpc.db.dbRemote.getPlayerById('*', data.player.id, (err, codeBalance, balanceRes) => {
                                        if (err) {
                                            logger.error(`[Step 4] Lỗi khi lấy thông tin tài khoản: ${err.message || err}`);
                                            // Vẫn gửi thông báo với thông tin mặc định
                                            sendNotification({ balance: 0 });
                                            return;
                                        }

                                        const playerInfo = {
                                            balance: balanceRes?.balance || 0
                                        };
                                        logger.info(`[Step 4] Đã lấy thông tin tài khoản của người chơi ${data.player.id}: ${playerInfo.balance}`);
                                        sendNotification(playerInfo);
                                    });
                                } else {
                                    // Lấy thông tin người chơi từ DB
                                    me.app.rpc.db.dbRemote.getPlayerById('*', data.player.id, (err, playerCode, playerData) => {
                                        if (err) {
                                            logger.error(`[Step 4] Lỗi khi lấy thông tin người chơi: ${err.message || err}`);
                                            // Vẫn gửi thông báo với thông tin mặc định
                                            sendNotification({ exp: 0, level: 1, vippoint: 0 });
                                            return;
                                        }

                                        const playerInfo = {
                                            exp: playerData?.exp || 0,
                                            level: playerData?.level || 1,
                                            vippoint: playerData?.vip_point || 0 // Sửa từ vippoint thành vip_point
                                        };
                                        logger.info(`[Step 4] Đã lấy thông tin người chơi ${data.player.id}: EXP=${playerInfo.exp}, VIP=${playerInfo.vippoint}`);
                                        sendNotification(playerInfo);
                                    });
                                }
                            }
                        } catch (notifyError) {
                            // Ghi log lỗi nhưng không ảnh hưởng đến kết quả trả về
                            logger.error('[Step 4] Lỗi khi gửi thông báo:', notifyError.message || notifyError);
                            logger.info('[Step 4] Tiếp tục xử lý mặc dù có lỗi khi gửi thông báo');
                        }

                        // Trả về kết quả thành công
                        callback(null, result);
                    } catch (error) {
                        logger.error('[Step 3] Lỗi khi xử lý phần thưởng:', error.message || error);
                        return cb(true, CODE.FAIL, "Lỗi khi xử lý phần thưởng");
                    }
                });
            });
        }
    ],
    function(err, result) {
        logger.info('[Final Step] Kết quả nhận thưởng từ thành tích:', result);
        if (err) {
            logger.error('[Final Step] Lỗi khi nhận thưởng từ thành tích:', err);
            return cb(true, CODE.FAIL, err);
        }

        logger.info('[Final Step] Hoàn tất nhận thưởng từ thành tích:', result.badge?.badge?.name);
        return cb(null, CODE.OK, result);
    });
};

/**
 * Lấy danh sách tất cả các thành tích trong hệ thống
 * @param {Object} payload - Dữ liệu đầu vào (type, userId)
 * @param {Function} cb - Callback function
 */
UsersService.prototype.getAllBadgesHandler = function(payload, cb) {
    logger.info("[UsersService.getAllBadgesHandler][Step 0] Lấy danh sách tất cả thành tích >> payload: ", payload);
    const type = payload.type; // Có thể là POKE_CAREER hoặc ACHIEVEMENTS
    const userId = payload.userId; // ID của người chơi từ session.uid

    const me = this;

    async.waterfall([
        // Bước 1: Kiểm tra player có tồn tại
        function(callback) {
            if (!userId) {
                logger.warn('[Step 1] Không có userId, chỉ lấy danh sách thành tích không có thông tin người chơi');
                callback(null, null);
                return;
            }

            me.app.rpc.db.dbRemote.getPlayerById('*', userId, (err, code, player) => {
                if (err || code !== CODE.OK) {
                    logger.error('[Step 1] Người chơi không tồn tại:', err);
                    callback(null, null); // Vẫn tiếp tục nhưng không có thông tin người chơi
                } else {
                    logger.info('[Step 1] Tìm thấy người chơi:', player?.id);
                    callback(null, player);
                }
            });
        },

        // Bước 2: Lấy danh sách tất cả thành tích từ DB
        function(player, callback) {
            logger.info('[Step 2] Lấy danh sách tất cả thành tích từ DB');

            // Tạo điều kiện truy vấn
            const queryOptions = {
                includeRewards: true // Bao gồm thông tin phần thưởng chi tiết
            };

            // Thêm điều kiện lọc theo type nếu có
            if (type) {
                queryOptions.type = type;
            }

            me.app.rpc.db.dbRemote.getAllBadges('*', queryOptions, (err, code, result) => {
                if (err) {
                    logger.error('[Step 2] Lỗi khi lấy danh sách thành tích:', err);
                    return cb(true, CODE.FAIL, "Lỗi khi truy vấn thành tích");
                } else {
                    logger.info('[Step 2] Kết quả thành tích:', result?.badges?.length);
                    callback(null, { player, badges: result.badges });
                }
            });
        },

        // Bước 3: Nếu có player, lấy danh sách thành tích của người chơi
        function(data, callback) {
            if (!data.player) {
                logger.info('[Step 3] Không có thông tin người chơi, bỏ qua bước lấy thành tích người chơi');
                callback(null, {
                    player: null,
                    badges: data.badges,
                    playerBadges: []
                });
                return;
            }

            logger.info('[Step 3] Lấy danh sách thành tích của người chơi:', data.player.id);

            // Lấy tất cả thành tích của người chơi (không phân trang)
            me.app.rpc.db.dbRemote.getPlayerBadges('*', {
                player_id: data.player.id,
                limit: 1000 // Lấy tất cả
            }, (err, code, result) => {
                logger.info('[Step 3] Lấy danh sách thành tích của người chơi >> result: ', result);
                if (err) {
                    logger.error('[Step 3] Lỗi khi lấy thành tích người chơi:', err);
                    // Vẫn tiếp tục nhưng không có thông tin thành tích người chơi
                    callback(null, {
                        player: data.player,
                        badges: data.badges,
                        playerBadges: []
                    });
                } else {
                    logger.info('[Step 3] Kết quả thành tích người chơi:', result?.badges?.length);
                    callback(null, {
                        player: data.player,
                        badges: data.badges,
                        playerBadges: result.badges || []
                    });
                }
            });
        },

        // Bước 4: Xử lý và định dạng dữ liệu kết quả
        function(data, callback) {
            // logger.info('[Step 4] Xử lý và định dạng dữ liệu kết quả >> data: ', data);

            // Nếu không có badges data
            if (!data.badges || data.badges.length === 0) {
                return callback(null, {
                    badges: []
                });
            }

            // Tạo map từ playerBadges để dễ dàng tìm kiếm
            const playerBadgesMap = {};
            data.playerBadges.forEach(pb => {
                logger.info(`[Step 4] Xử lý playerBadge: `, pb);
                // xóa key là badge (vì không cần hiển thị cái này), badge 1 object của item badges
                if (pb.badge) {
                    delete pb.badge;
                }
                playerBadgesMap[pb.badge_id] = pb;
            });

            // Đối với mỗi badge, tạo một đối tượng BadgeDTO và thêm thông tin player_reward
            const formattedBadges = data.badges.map(badge => {
                logger.info(`[Step 4] Xử lý badge: `, badge);
                // Tìm thành tích tương ứng của người chơi (nếu có)
                const playerBadge = playerBadgesMap[badge.id] || null;
                // logger.info(`[Step 4] Tìm thành tích tương ứng của người chơi: `, playerBadge);
                // Chuyển đổi dữ liệu thô thành BadgeDTO
                const badgeDTO = new BadgeDTO({
                    id: badge.id,
                    badge_id: badge.id,
                    type: badge.type,
                    name: badge.name,
                    code: badge.code,
                    description: badge.description,
                    // condition_json: badge.condition_json, // không cần trả về cái này cho client
                    reward: badge.reward,
                    category: badge.category,
                    icon_url: badge.icon_url,
                    created_at: badge.created_at,
                    rewards: badge.rewards || [], // Thêm danh sách phần thưởng chi tiết
                    claimed_at: playerBadge ? playerBadge.claimed_at : null,
                    awarded_at: playerBadge ? playerBadge.awarded_at : null
                });

                // Thêm thông tin player_reward
                badgeDTO.player_reward = playerBadge;

                return badgeDTO;
            });

            // Trả về kết quả đã định dạng
            callback(null, {
                badges: formattedBadges
            });
        }
    ],
    function(err, result) {
        if (err) {
            logger.error('[Final Step] Lỗi khi xử lý danh sách thành tích:', err);
            return cb(true, CODE.FAIL, err);
        }

        logger.info('[Final Step] Hoàn tất lấy danh sách tất cả thành tích, số lượng:', result.badges?.length);
        return cb(null, CODE.OK, result);
    });
};

/**
 * Cập nhật thông tin người chơi
 * @param {Object} data - Dữ liệu cần cập nhật
 * @param {Object} data.id - ID của người chơi
 * @param {String} data.phone - Số điện thoại của người chơi
 * @param {String} data.avatar - Avatar của người chơi
 * @param {String} data.gender - Giới tính của người chơi
 * @param {String} data.displayName - Tên hiển thị của người chơi
 * @param {Function} cb - Callback function
 */
UsersService.prototype.updatePlayerInfo = function(data, cb) {
    const me = this;
    logger.info('[usersService.updatePlayerInfo][Step 0] >> data: ', data);
    if (!data.id) {
        return cb(true, CODE.FAIL, 'Missing player ID');
    }
    const uid = data.id;
    const updateData = {};
    
    // Chỉ cập nhật các trường được gửi lên
    if (data.displayName) {
        // const { firstName, lastName } = utils.splitFullName(data.displayName);
        // if (firstName) updateData.first_name = firstName;
        // if (lastName) updateData.last_name = lastName;
        updateData.display_name = data.displayName;
    }
    // if (data.firstName) updateData.first_name = data.firstName;
    // if (data.lastName) updateData.last_name = data.lastName;
    if (data.phone) updateData.phone = data.phone;
    // if (data.email) updateData.email = data.email;
    if (data.avatar) updateData.avatar = data.avatar;
    if (data.gender) updateData.gender = data.gender;
    // if (data.birthday) updateData.birthday = data.birthday;
    
    updateData.updated_at = new Date();

    logger.info('[usersService.updatePlayerInfo][Step 0] >> updateData: ', updateData);

    async.waterfall([
        // Step 1: Update player in DB
        function(callback) {
            me.app.get('dbService').updatePlayer(uid, updateData, function(err, result) {
                logger.info('[usersService.updatePlayerInfo][Step 1] >> result: ', result);
                if (err) {
                    logger.error('[usersService.updatePlayerInfo][Step 1] >> Error updating player info:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                    return callback(err);
                }
                callback(null, result);
            });
        },

        // Step 2: Update user in Auth service if needed
        function(result, callback) {
            logger.info('[usersService.updatePlayerInfo][Step 2] >> result: ', result, ' -> data: ', data);
            if (!data.displayName && !data.phone && !data.avatar && !data.gender) {
                return callback(null, result);
            }

            const dataUpdateRPC = {
                id: uid,
                firstName: data.displayName ? utils.splitFullName(data.displayName).firstName : '',
                lastName: data.displayName ? utils.splitFullName(data.displayName).lastName : '',
                phone: data.phone,
                avatar: data.avatar,
                gender: data.gender,
                lastLogin: utils.getCurrentTimestamp()
            };

            logger.info('[usersService.updatePlayerInfo][Step 2] >> dataUpdateRPC: ', dataUpdateRPC);
            me.app.rpc.authen.authenRemote.updateUser('*', dataUpdateRPC, function(err, code, authResult) {
                logger.info('[usersService.updatePlayerInfo][Step 2] >> result: ', authResult, ' -> code: ', code, ' -> err: ', err);
                if (err || code !== CODE.OK) {
                    return callback(err || 'Auth update failed');
                }
                callback(null, authResult);
            });
        },
        // Step 3: Get updated player info
        function(result, callback) {
            logger.info('[usersService.updatePlayerInfo][Step 3] >> Get updated player info with result: ', result);
            me.app.rpc.db.dbRemote.getPlayerById('*', uid, function(err, code, player) {
                if (err) {
                    logger.error('[usersService.updatePlayerInfo][Step 3] >> Error getting updated player:', err);
                    return callback(err);
                }
                logger.info('[usersService.updatePlayerInfo][Step 3] >> Updated player:', player);
                callback(null, player);
            });
        },
    ], function(err, result) {
        logger.info('[usersService.updatePlayerInfo][Step 4] >> result: ', result);
        if (err) {
            return cb(true, CODE.FAIL, err);
        }
        return cb(null, CODE.OK, result);
    });
};

UsersService.prototype.updatePassword = function(data, cb) {
    const me = this;
    logger.info('[usersService.updatePassword][Step 0] >> data: ', data);
    if (!data.id) {
        return cb(true, CODE.FAIL, 'Missing player ID');
    }
    const uid = data.id;
    const updateData = {};
    if (data.new_password) updateData.new_password = data.new_password;
    if (data.old_password) updateData.old_password = data.old_password;
    if (data.user_id) updateData.user_id = data.user_id;
    // updateData.updated_at = new Date();

    logger.info('[usersService.updatePassword][Step 0] >> updateData: ', updateData);

    async.waterfall([
        // Step 1: Update player in DB
        function(callback) {
            // me.app.get('dbService').updatePlayer(uid, updateData, function(err, result) {
            //     logger.info('[usersService.updatePassword][Step 1] >> result: ', result);
            //     if (err) {
            //         logger.error('[usersService.updatePassword][Step 1] >> Error updating player info:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
            //         return callback(err);
            //     }
            //     callback(null, result);
            // });
            me.app.rpc.authen.authenRemote.updatePassword('*', updateData, (err, codeUserInfo, resUserInfo) => {

                logger.info("[usersService.updatePassword] with err: ", err, " code: ", codeUserInfo, " and res: ", resUserInfo);

                if (err) {
                    logger.error('[usersService.updatePassword][Step 1] >> Error updating password of player info:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                    return callback(err);
                }
                
                callback(null, codeUserInfo, resUserInfo?.data ?? null);
            });
        },
    ], function(err, code, result) {
        logger.info('[usersService.updatePassword][Step 2] >> result: ', result);
        if (err) {
            return cb(true, CODE.FAIL, err);
        }
        return cb(null, CODE.OK, result);
    });
};

/**
 * Update tutorial completion status for a player
 * @param {Number} playerId - Player ID 
 * @param {Function} cb - Callback function
 */
UsersService.prototype.completeTutorialHandler = function(playerId, cb) {
    logger.info("[UsersService.completeTutorialHandler][Step 0] Update tutorial completion status for player: ", playerId);
    
    const me = this;

    var expService = this.app.get('expService');
    let currentBalance = 0;
    
    async.waterfall([
        // Step 1: Check if player exists
        function(callback) {
            if (!playerId) {
                logger.error('[Step 1] No player ID provided');
                return callback('No player ID provided');
            }
            
            me.app.rpc.db.dbRemote.getPlayerById('*', playerId, (err, code, player) => {
                if (err || code !== CODE.OK) {
                    logger.error('[Step 1] Player not found:', err);
                    return callback('Player not found');
                }
                
                logger.info('[Step 1] Found player:', player?.id, ' -> player: ', player);
                currentBalance = player?.balance || 0;
                callback(null, player);
            });
        },
        
        // Step 2: Update tutorial completion status
        function(player, callback) {
            logger.info('[Step 2] Updating tutorial completion status for player:', player.id);
            
            // Update properties.tutorial_completed = true
            me.app.rpc.db.dbRemote.updateProperties('*', player.id, { tutorial_completed: true }, (err, code, result) => {
                if (err || code !== CODE.OK) {
                    logger.error('[Step 2] Error updating tutorial status:', err);
                    return callback('Error updating tutorial status');
                }
                
                logger.info('[Step 2] Successfully updated tutorial status');
                callback(null, { 
                    player_id: player.id,
                    tutorial_completed: true
                });
            });
        }, 

        // Step 3: cộng balance và exp cho người chơi
        function(result, callback) {
            logger.info('[Step 3] Cộng balance và exp cho người chơi:', result.player_id, ' -> result: ', result);
            // result: { player_id: 5, tutorial_completed: true }
            
            // Update properties.tutorial_completed = true
            const balanceIncrement = 20000000;
            const expIncrement = 50;
            me.app.rpc.db.dbRemote.incrementPlayerStats('*', result.player_id, { balance: balanceIncrement, exp: expIncrement }, (err, code, res) => {
                logger.info('[Step 3] incrementPlayerStats: ', err, ' -> code: ', code, ' -> res: ', res);
                if (err || code !== CODE.OK) {
                    logger.error('[Step 3] Error updating balance and exp:', err);
                    return callback('Error updating balance and exp');
                }

                // add transactions logs
                me.app.get('sync').flush('playerSync.createTransaction', result.player_id, {
                    player_id: result.player_id,
                    amount: balanceIncrement,
                    before_balance: currentBalance,
                    after_balance: currentBalance + balanceIncrement,
                    type: LogTypes.SYSTEM,
                    action: LogBoards.TUTORIAL_REWARD,
                    reference_id: result.player_id,
                    reference_type: LogBoards.TUTORIAL_REWARD,
                    meta: {
                        tutorial: true,
                        reward_amount: balanceIncrement
                    },
                    description: 'Phần thưởng từ tutorial'
                });

                expService.addExp({
                    playerId: result.player_id,
                    expValue: expIncrement,
                    source: 'TUTORIAL',
                    metadata: {
                        tutorial: true
                    }
                }, (err, code, res2) => {
                    if (err || code !== CODE.OK) {
                        logger.error('[Step 3] Error adding exp:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                        return callback('Error adding exp');
                    }
                    
                    logger.info('[Step 3] Successfully added ex >> res2: ', res2);
                    result.balance = currentBalance + balanceIncrement
                    callback(null, result);
                });
                
                // logger.info('[Step 3] Successfully updated balance and exp >> result: ', result);
                // callback(null, result);
            });
        }
    ],
    function(err, result) {
        if (err) {
            logger.error('[Final Step] Error in completeTutorialHandler:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
            return cb(true, CODE.FAIL, err);
        }
        
        logger.info('[Final Step] Successfully completed tutorial for player:', result.player_id);
        return cb(null, CODE.OK, result);
    });
};

// make function getTokenUploadHandler
UsersService.prototype.getTokenUploadHandler = function (uid, cb) {
    logger.info("[usersService.getTokenUploadHandler] with uid: ", uid);
    const me = this;

    // make async.waterfall , step 1: get player info, step 2 call to endpoint web for make token, get token data by format 
    /**
     * {
    "success": true,
    "message": "External user token created successfully",
    "token": "ext_n3dYuAKKyhHKl3TMfclFgOrsu5E36Dxb_1748943442",
    "expires_in": 86400,
    "expires_at": "2025-06-04T09:37:22.120192Z",
    "user_info": {
        "game_uid": 5,
        "username": "betapcode",
        "external_system": "Game Server"
    }
     */
    async.waterfall([
        // Step 1: Get player info
        function(callback) {
            me.app.rpc.db.dbRemote.getPlayerById('*', uid, (err, code, player) => {
                if (err || code !== CODE.OK) {
                    logger.error('[Step 1] Error getting player info:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                    return callback(err);
                }
                logger.info('[Step 1] Found player:', player?.id);
                callback(null, player);
            });
        },
        // Step 2: Call to endpoint web for make token
        // make function call to https://dash.pokee.club/api/v1/external/tokens/create with method POST
        // with body: {
        //     "game_uid": 5,
        //     "username": "betapcode",
        //     "external_system": "Game Server"
        // }
        // return token data
        // get token data by format 
        // {
        //     "success": true,
        //     "message": "External user token created successfully",
        //     "token": "ext_n3dYuAKKyhHKl3TMfclFgOrsu5E36Dxb_1748943442",
        //     "expires_in": 86400,
        //     "expires_at": "2025-06-04T09:37:22.120192Z",
        //     "user_info": {
        //         "game_uid": 5,
        //         "username": "betapcode",
        //         "external_system": "Game Server"
        //     }
        // }
        // return token data
        // Step 2: Call to endpoint web for make token

        function(player, callback) {
            logger.info('[Step 2] Calling to endpoint web for make token with player:', player?.id);
            me.app.rpc.authen.authenRemote.getTokenUpload('*', player?.id, (err, code, res) => {
                if (err || code !== CODE.OK) {
                    logger.error('[Step 2] Error getting token upload:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                    return callback(err);
                }
                logger.info('[Step 2] Got token upload:', res);
                callback(null, res);
            });
        }
    ],
    function(err, result) {
        if (err) {
            logger.error('[Final Step] Error in getTokenUploadHandler:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
            return cb(true, CODE.FAIL, err);
        }
        return cb(null, CODE.OK, result);
    });
};